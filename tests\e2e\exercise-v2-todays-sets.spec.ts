import { test, expect } from '@playwright/test'
import { mockRecommendationResponse } from './helpers/recommendation-mocks'

test.describe('Exercise V2 - Todays Sets Section', () => {
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 812 })

    // Mock API responses
    await page.route('**/api/userInfo', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Email: '<EMAIL>',
          FirstName: 'Test',
          MassUnit: 'kg',
        }),
      })
    })

    await page.route('**/GetRecommendation*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockRecommendationResponse),
      })
    })

    await page.route('**/api/exercise/*/recommendation', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockRecommendationResponse),
      })
    })

    // Mock workout API
    await page.route('**/api/workout/current', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          WorkoutTemplateModel: {
            Id: 1,
            Label: 'Test Workout',
            Exercises: [
              {
                Id: 1,
                Label: 'Bench Press',
                BodyPartId: 1,
              },
            ],
          },
        }),
      })
    })

    // Navigate to exercise page
    await page.goto('/workout/exercise-v2/1')
  })

  test('should display "Today\'s sets" section at the bottom', async ({
    page,
  }) => {
    // Wait for page to load
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Check for Today's sets heading
    const todaysSetsHeading = page.locator('text="Today\'s sets"')
    await expect(todaysSetsHeading).toBeVisible()

    // Verify it's at the bottom of the page
    const todaysSetsSection = todaysSetsHeading.locator('..')
    const bottomArea = page.locator('.flex-\\[1\\]').last()
    await expect(bottomArea).toContainElement(todaysSetsSection)
  })

  test('should show all sets in a single list', async ({ page }) => {
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Find the Today's sets section
    const todaysSetsSection = page.locator('text="Today\'s sets"').locator('..')

    // Should see warmup sets (W1, W2) and work sets (Set 1, Set 2, etc)
    await expect(todaysSetsSection.locator('text="W1"')).toBeVisible()
    await expect(todaysSetsSection.locator('text="W2"')).toBeVisible()
    await expect(todaysSetsSection.locator('text="Set 1"')).toBeVisible()
    await expect(todaysSetsSection.locator('text="Set 2"')).toBeVisible()
  })

  test('should highlight the current active set', async ({ page }) => {
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Find the first set (should be active)
    const firstSet = page.locator('[data-testid="set-row"]').first()

    // Check for active indicator (ring styling)
    await expect(firstSet).toHaveClass(/ring-2/)
    await expect(firstSet).toHaveClass(/ring-primary-600/)

    // Should also have the pulsing dot indicator
    const activeIndicator = firstSet.locator('.animate-pulse')
    await expect(activeIndicator).toBeVisible()
  })

  test('should show checkmark for completed sets', async ({ page }) => {
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Complete the first set
    await page.click('button:has-text("Save set")')

    // Wait for set to be marked complete
    await page.waitForTimeout(500)

    // First set should now have a checkmark
    const firstSet = page.locator('[data-testid="set-row"]').first()
    const checkmark = firstSet.locator('svg.text-green-500')
    await expect(checkmark).toBeVisible()

    // Second set should now be active
    const secondSet = page.locator('[data-testid="set-row"]').nth(1)
    await expect(secondSet).toHaveClass(/ring-2/)
  })

  test('should NOT show separate "Completed Sets" or "Next sets" sections', async ({
    page,
  }) => {
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // These old sections should not exist
    await expect(page.locator('text="Completed Sets"')).not.toBeVisible()
    await expect(page.locator('text="Next sets"')).not.toBeVisible()

    // Only "Today's sets" should be visible
    await expect(page.locator('text="Today\'s sets"')).toBeVisible()
  })
})
