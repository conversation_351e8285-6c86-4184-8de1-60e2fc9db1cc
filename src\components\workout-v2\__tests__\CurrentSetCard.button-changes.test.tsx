import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { CurrentSetCard } from '../CurrentSetCard'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  useAnimation: () => ({
    start: vi.fn(),
  }),
}))

const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsTimeBased: false,
  IsFinished: false,
}

const mockCurrentSet: WorkoutLogSerieModel = {
  Id: 1,
  Reps: 10,
  Weight: { Kg: 80, Lb: 175 },
  IsWarmups: false,
  IsNext: true,
  IsFinished: false,
}

const defaultProps = {
  exercise: mockExercise,
  currentSet: mockCurrentSet,
  setData: { reps: 10, weight: 80, duration: 0 },
  onSetDataChange: vi.fn(),
  onComplete: vi.fn(),
  onSkip: vi.fn(),
  isSaving: false,
  completedSets: 0,
  totalSets: 3,
  unit: 'kg' as const,
}

describe('CurrentSetCard - Button Changes', () => {
  it('should show only "Save set" button, not "Skip" button', () => {
    // Given: CurrentSetCard is rendered
    render(<CurrentSetCard {...defaultProps} />)

    // When: User views action buttons
    // Then: Only "Save set" button is visible, no "Skip" button
    expect(
      screen.getByRole('button', { name: /save set/i })
    ).toBeInTheDocument()
    expect(
      screen.queryByRole('button', { name: /skip/i })
    ).not.toBeInTheDocument()
  })

  it('should call onComplete when "Save set" button is clicked', async () => {
    // Given: CurrentSetCard is rendered with onComplete handler
    const mockOnComplete = vi.fn()
    render(<CurrentSetCard {...defaultProps} onComplete={mockOnComplete} />)

    // When: User clicks "Save set" button
    await userEvent.click(screen.getByRole('button', { name: /save set/i }))

    // Then: onComplete callback is triggered
    expect(mockOnComplete).toHaveBeenCalledOnce()
  })

  it('should disable "Save set" button when isSaving is true', () => {
    // Given: CurrentSetCard is in saving state
    render(<CurrentSetCard {...defaultProps} isSaving />)

    // When: User views the save button (text changes to "Saving...")
    const saveButton = screen.getByRole('button', { name: /saving/i })

    // Then: Button should be disabled
    expect(saveButton).toBeDisabled()
  })

  it('should show updated swipe hint with both skip and complete actions', () => {
    // Given: CurrentSetCard is rendered
    render(<CurrentSetCard {...defaultProps} />)

    // When: User views swipe instructions
    // Then: Should show updated swipe instruction with both skip and complete
    expect(
      screen.getByText('Swipe left to skip · right to complete')
    ).toBeInTheDocument()
  })
})
