'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { vibrate } from '@/utils/haptics'
import { CustomDurationModal } from './CustomDurationModal'

interface DurationPickerProps {
  currentDuration: number
  onSelect: (duration: number) => void
  onClose: () => void
}

export function DurationPicker({
  currentDuration,
  onSelect,
  onClose,
}: DurationPickerProps) {
  const [showCustomModal, setShowCustomModal] = useState(false)

  const durations = [
    { seconds: 30, label: '0:30' },
    { seconds: 60, label: '1:00' },
    { seconds: 90, label: '1:30' },
    { seconds: 120, label: '2:00' },
    { seconds: 180, label: '3:00' },
    { seconds: 300, label: '5:00' },
  ]

  const handleSelect = (duration: number) => {
    vibrate('medium') // More noticeable feedback
    onSelect(duration)
    onClose()
  }

  return (
    <>
      <AnimatePresence>
        {!showCustomModal && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 z-40"
              onClick={onClose}
            />

            {/* Picker */}
            <motion.div
              initial={{ y: 100, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: 100, opacity: 0 }}
              transition={{ type: 'spring', damping: 25, stiffness: 300 }}
              data-testid="duration-picker"
              className="fixed bottom-0 left-0 right-0 bg-surface-primary rounded-t-3xl shadow-xl z-50 px-4 pb-8 pt-4"
            >
              {/* Handle bar */}
              <div className="w-12 h-1 bg-surface-tertiary rounded-full mx-auto mb-6" />

              <h3 className="text-lg font-semibold text-text-primary mb-4">
                Rest Duration
              </h3>

              <div className="grid grid-cols-3 gap-3">
                {durations.map(({ seconds, label }) => (
                  <button
                    key={seconds}
                    onClick={() => handleSelect(seconds)}
                    data-testid={`duration-option-${seconds}`}
                    className={`
                  py-4 px-6 rounded-2xl font-medium text-base
                  transition-all duration-200 min-h-[48px] min-w-[48px]
                  touch-manipulation active:scale-95
                  ${
                    currentDuration === seconds
                      ? 'bg-gradient-to-r from-brand-gold-start to-brand-gold-end text-text-inverse shadow-lg'
                      : 'bg-surface-secondary text-text-primary hover:bg-surface-tertiary hover:shadow-md'
                  }
                `}
                  >
                    {label}
                  </button>
                ))}

                {/* Custom duration button */}
                <button
                  onClick={() => {
                    vibrate('light')
                    setShowCustomModal(true)
                  }}
                  data-testid="duration-option-custom"
                  className="py-4 px-6 rounded-2xl font-medium text-base
                       bg-surface-secondary text-text-primary
                       hover:bg-surface-tertiary hover:shadow-md
                       transition-all duration-200 min-h-[48px] min-w-[48px]
                       touch-manipulation active:scale-95"
                >
                  Custom
                </button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Custom Duration Modal */}
      {showCustomModal && (
        <CustomDurationModal
          currentDuration={currentDuration}
          onSelect={(duration) => {
            handleSelect(duration)
            setShowCustomModal(false)
          }}
          onClose={() => {
            setShowCustomModal(false)
            onClose() // Also close the duration picker
          }}
        />
      )}
    </>
  )
}
