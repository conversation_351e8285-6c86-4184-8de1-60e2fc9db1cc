'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { SkipForward, Clock } from 'lucide-react'
import { useWorkoutStore } from '@/stores/workoutStore'
import { vibrate } from '@/utils/haptics'
import { DurationPicker } from './DurationPicker'

export function RestTimer() {
  const { restTimerState, setRestTimerState } = useWorkoutStore()
  const [timeRemaining, setTimeRemaining] = useState(0)
  const [showDurationPicker, setShowDurationPicker] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (restTimerState.isActive && restTimerState.duration > 0) {
      setTimeRemaining(restTimerState.duration)

      // Clear any existing interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }

      // Start countdown
      intervalRef.current = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            // Timer complete
            vibrate('success')
            setRestTimerState({ isActive: false, duration: 0 })

            // Show notification if available
            if (
              'Notification' in window &&
              Notification.permission === 'granted'
            ) {
              try {
                const notification = new Notification('Rest Complete!', {
                  body: 'Time for your next set',
                  icon: '/icon-192x192.png',
                  tag: 'rest-timer',
                })
                // Close notification after 5 seconds
                setTimeout(() => notification.close(), 5000)
              } catch (error) {
                // Handle notification constructor errors gracefully
                console.warn('Failed to show rest timer notification:', error)
              }
            }

            return 0
          }

          // Vibrate at 10 seconds
          if (prev === 11) {
            vibrate('warning')
          }

          return prev - 1
        })
      }, 1000)

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }
      }
    }
    return undefined
  }, [restTimerState, setRestTimerState])

  const skipRest = () => {
    vibrate('light')
    setRestTimerState({ isActive: false, duration: 0 })
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleDurationSelect = (duration: number) => {
    // Save to localStorage for future use
    localStorage.setItem('restDuration', duration.toString())
    // Dispatch storage event for same-tab sync
    window.dispatchEvent(new Event('storage'))

    // Restart the current timer with the new duration
    setRestTimerState({
      isActive: true,
      duration,
      nextSetInfo: restTimerState.nextSetInfo,
    })

    // Reset the countdown immediately
    setTimeRemaining(duration)

    // Provide haptic feedback
    vibrate('light')
  }

  if (!restTimerState.isActive) return null

  const progress = (timeRemaining / restTimerState.duration) * 100

  return (
    <>
      <AnimatePresence>
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          data-testid="rest-timer-container"
          className="fixed bottom-0 left-0 right-0 bg-surface-primary/90 backdrop-blur-sm shadow-lg border-t border-surface-tertiary"
        >
          <div className="px-4 py-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex-1">
                <div className="flex items-center gap-3">
                  <h3 className="text-lg font-semibold text-text-primary">
                    Rest
                  </h3>
                  <button
                    onClick={() => setShowDurationPicker(true)}
                    data-testid="duration-setting-button"
                    className="flex items-center gap-2 px-4 py-2 bg-surface-secondary rounded-full 
                           text-sm text-text-secondary hover:bg-surface-tertiary transition-all
                           min-h-[44px] min-w-[44px] touch-manipulation"
                  >
                    <Clock className="w-4 h-4" />
                    <span data-testid="duration-setting">
                      {formatTime(restTimerState.duration)}
                    </span>
                  </button>
                </div>
                <p
                  data-testid="countdown-text"
                  className="text-2xl font-bold bg-gradient-to-r from-brand-gold-start to-brand-gold-end bg-clip-text text-transparent"
                >
                  {formatTime(timeRemaining)}
                </p>
              </div>

              <button
                onClick={skipRest}
                className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-brand-gold-start to-brand-gold-end 
                         text-text-inverse rounded-full font-medium text-sm
                         hover:opacity-90 transition-all"
              >
                <SkipForward className="w-4 h-4" />
                Hide
              </button>
            </div>

            {/* Progress bar */}
            <div className="h-2 bg-surface-secondary rounded-full overflow-hidden">
              <motion.div
                data-testid="rest-timer-progress"
                className="h-full bg-gradient-to-r from-brand-gold-start to-brand-gold-end"
                initial={{ width: '100%' }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Duration Picker */}
      {showDurationPicker && (
        <DurationPicker
          currentDuration={restTimerState.duration}
          onSelect={handleDurationSelect}
          onClose={() => setShowDurationPicker(false)}
        />
      )}
    </>
  )
}

// Helper hook to start rest timer
export function useRestTimer() {
  const { setRestTimerState } = useWorkoutStore()

  const startRestTimer = (
    duration: number,
    nextSetInfo?: { reps: number; weight: number; unit: 'kg' | 'lbs' }
  ) => {
    // Request notification permission if not granted
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }

    setRestTimerState({ isActive: true, duration, nextSetInfo })
  }

  return { startRestTimer }
}
