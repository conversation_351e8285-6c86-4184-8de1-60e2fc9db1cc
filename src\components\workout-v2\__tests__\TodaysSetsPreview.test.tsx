import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import { TodaysSetsPreview } from '../TodaysSetsPreview'
import type { WorkoutLogSerieModel } from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'

// Extended type for testing
type ExtendedWorkoutLogSerieModel = WorkoutLogSerieModel &
  Partial<WorkoutLogSerieModelRef> & {
    WarmUpReps?: number
    WarmUpWeightSet?: { Lb: number; Kg: number }
    IsSkipped?: boolean
    IsNext?: boolean
    IsFinished?: boolean
  }

describe('TodaysSetsPreview', () => {
  const mockCompletedSet: ExtendedWorkoutLogSerieModel = {
    Id: 1,
    Reps: 10,
    Weight: { Kg: 50, Lb: 110 },
    IsWarmups: false,
    IsFinished: true,
    IsNext: false,
  }

  const mockActiveSet: ExtendedWorkoutLogSerieModel = {
    Id: 2,
    Reps: 8,
    Weight: { Kg: 60, Lb: 132 },
    IsWarmups: false,
    IsFinished: false,
    IsNext: true,
  }

  const mockNextSet: ExtendedWorkoutLogSerieModel = {
    Id: 3,
    Reps: 6,
    Weight: { Kg: 65, Lb: 143 },
    IsWarmups: false,
    IsFinished: false,
    IsNext: false,
  }

  const mockWarmupSet: ExtendedWorkoutLogSerieModel = {
    Id: 4,
    WarmUpReps: 5,
    WarmUpWeightSet: { Kg: 30, Lb: 66 },
    Weight: { Kg: 0, Lb: 0 },
    IsWarmups: true,
    IsFinished: true,
    IsNext: false,
  }

  it('renders "Today\'s sets" heading', () => {
    render(
      <TodaysSetsPreview
        allSets={[mockCompletedSet, mockActiveSet, mockNextSet]}
        unit="kg"
      />
    )

    expect(screen.getByText("Today's sets")).toBeInTheDocument()
  })

  it('shows all sets in a single list', () => {
    render(
      <TodaysSetsPreview
        allSets={[mockCompletedSet, mockActiveSet, mockNextSet]}
        unit="kg"
      />
    )

    // Should show all 3 sets
    expect(screen.getByText('Set 1')).toBeInTheDocument()
    expect(screen.getByText('Set 2')).toBeInTheDocument()
    expect(screen.getByText('Set 3')).toBeInTheDocument()
  })

  it('shows green checkmark for completed sets', () => {
    render(
      <TodaysSetsPreview
        allSets={[mockCompletedSet, mockActiveSet, mockNextSet]}
        unit="kg"
      />
    )

    // First set (completed) should have checkmark
    const completedSetElement = screen.getByText('Set 1').closest('div')
    const checkmark = completedSetElement?.querySelector('svg')
    expect(checkmark).toHaveClass('text-green-500')
  })

  it('shows active set indicator for current set', () => {
    render(
      <TodaysSetsPreview
        allSets={[mockCompletedSet, mockActiveSet, mockNextSet]}
        unit="kg"
      />
    )

    // Active set should have gold gradient dot indicator
    const activeSetElement = screen
      .getByText('Set 2')
      .closest('[data-testid="set-row"]')
    const dotIndicator = activeSetElement?.querySelector(
      '.bg-gradient-metallic-gold'
    )
    expect(dotIndicator).toBeInTheDocument()
    expect(dotIndicator).toHaveClass('animate-pulse')
  })

  it('displays warmup sets with "W" prefix', () => {
    render(
      <TodaysSetsPreview
        allSets={[mockWarmupSet, mockCompletedSet]}
        unit="kg"
      />
    )

    expect(screen.getByText('W1')).toBeInTheDocument()
    expect(screen.getByText('5 reps')).toBeInTheDocument()
    expect(screen.getByText('30 kg')).toBeInTheDocument()
  })

  it('renders nothing when no sets provided', () => {
    const { container } = render(<TodaysSetsPreview allSets={[]} unit="kg" />)

    expect(container.firstChild).toBeNull()
  })

  it('correctly displays weight in selected unit', () => {
    render(
      <TodaysSetsPreview
        allSets={[mockCompletedSet, mockActiveSet]}
        unit="lbs"
      />
    )

    expect(screen.getByText('110 lbs')).toBeInTheDocument()
    expect(screen.getByText('132 lbs')).toBeInTheDocument()
  })

  describe('decimal formatting', () => {
    it('formats weights with excessive decimals to max 2 decimal places', () => {
      const mockSetWithDecimals: ExtendedWorkoutLogSerieModel = {
        Id: 5,
        Reps: 10,
        Weight: { Kg: 61.123456789, Lb: 135.123456789 },
        IsWarmups: false,
        IsFinished: false,
        IsNext: true,
      }

      render(<TodaysSetsPreview allSets={[mockSetWithDecimals]} unit="lbs" />)

      // Should format to max 2 decimal places
      expect(screen.getByText('135.12 lbs')).toBeInTheDocument()
    })

    it('preserves single decimal place values', () => {
      const mockSetOneDecimal: ExtendedWorkoutLogSerieModel = {
        Id: 6,
        Reps: 8,
        Weight: { Kg: 45.5, Lb: 100.5 },
        IsWarmups: false,
        IsFinished: false,
        IsNext: false,
      }

      render(<TodaysSetsPreview allSets={[mockSetOneDecimal]} unit="lbs" />)

      expect(screen.getByText('100.5 lbs')).toBeInTheDocument()
    })

    it('removes trailing zeros from decimal values', () => {
      const mockSetTrailingZeros: ExtendedWorkoutLogSerieModel = {
        Id: 7,
        Reps: 12,
        Weight: { Kg: 50.0, Lb: 110.0 },
        IsWarmups: false,
        IsFinished: false,
        IsNext: false,
      }

      render(<TodaysSetsPreview allSets={[mockSetTrailingZeros]} unit="kg" />)

      // Should display as "50 kg" not "50.00 kg"
      expect(screen.getByText('50 kg')).toBeInTheDocument()
    })

    it('formats warmup set weights with proper decimals', () => {
      const mockWarmupWithDecimals: ExtendedWorkoutLogSerieModel = {
        Id: 8,
        WarmUpReps: 5,
        WarmUpWeightSet: { Kg: 27.85432, Lb: 61.4159874 },
        Weight: { Kg: 0, Lb: 0 },
        IsWarmups: true,
        IsFinished: false,
        IsNext: false,
      }

      render(<TodaysSetsPreview allSets={[mockWarmupWithDecimals]} unit="kg" />)

      expect(screen.getByText('27.85 kg')).toBeInTheDocument()
    })

    it('handles zero weight values correctly', () => {
      const mockZeroWeight: ExtendedWorkoutLogSerieModel = {
        Id: 9,
        Reps: 10,
        Weight: { Kg: 0, Lb: 0 },
        IsWarmups: false,
        IsFinished: false,
        IsNext: false,
      }

      render(<TodaysSetsPreview allSets={[mockZeroWeight]} unit="lbs" />)

      expect(screen.getByText('0 lbs')).toBeInTheDocument()
    })
  })
})
