import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { CurrentSetCard } from '../CurrentSetCard'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  useAnimation: () => ({
    start: vi.fn(),
  }),
}))

const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsTimeBased: false,
  IsFinished: false,
}

const mockCurrentSet: WorkoutLogSerieModel = {
  Id: 1,
  Reps: 10,
  Weight: { Kg: 80, Lb: 175 },
  IsWarmups: false,
  IsNext: true,
  IsFinished: false,
}

const defaultProps = {
  exercise: mockExercise,
  currentSet: mockCurrentSet,
  setData: { reps: 10, weight: 80, duration: 0 },
  onSetDataChange: vi.fn(),
  onComplete: vi.fn(),
  onSkip: vi.fn(),
  isSaving: false,
  completedSets: 1,
  unit: 'kg' as const,
}

describe('CurrentSetCard - Spacing Improvements', () => {
  describe('Explainer Text Spacing', () => {
    it('should have reduced spacing between explainer text and save button', () => {
      // Given: CurrentSetCard is rendered
      render(<CurrentSetCard {...defaultProps} />)

      // When: User views the explainer text and save button
      const explainerText = screen.getByText(
        'Swipe left to skip · right to complete'
      )
      const saveButton = screen.getByText('Save set')

      // Then: The explainer container should have mt-2 class (reduced from mt-4)
      const explainerContainer = explainerText.parentElement
      expect(explainerContainer).toHaveClass('mt-2')

      // And: The button wrapper should have mt-2 class (reduced from mt-4)
      const buttonWrapper = saveButton.closest('div.w-full')
      expect(buttonWrapper).toHaveClass('mt-2')
    })
  })

  describe('Explainer Text Size', () => {
    it('should display explainer text with smaller font size', () => {
      // Given: CurrentSetCard is rendered
      render(<CurrentSetCard {...defaultProps} />)

      // When: User views the explainer text
      const explainerText = screen.getByText(
        'Swipe left to skip · right to complete'
      )

      // Then: Text should have text-base class (reduced from text-lg)
      expect(explainerText).toHaveClass('text-base')
      expect(explainerText).not.toHaveClass('text-lg')
    })
  })

  describe('Label Spacing Alignment', () => {
    it('should have consistent bottom spacing for REPS and weight unit labels', () => {
      // Given: CurrentSetCard is rendered
      render(<CurrentSetCard {...defaultProps} />)

      // When: User views the labels
      const repsLabel = screen.getByText('REPS')
      const unitLabel = screen.getByText('KG')

      // Then: Both labels should have -bottom-6 positioning
      expect(repsLabel).toHaveClass('-bottom-6')
      expect(unitLabel).toHaveClass('-bottom-6')
      expect(unitLabel).not.toHaveClass('-bottom-8')
    })

    it('should maintain consistent spacing with LBS unit', () => {
      // Given: CurrentSetCard is rendered with lbs unit
      render(
        <CurrentSetCard
          {...defaultProps}
          unit="lbs"
          setData={{ reps: 10, weight: 175, duration: 0 }}
        />
      )

      // When: User views the labels
      const repsLabel = screen.getByText('REPS')
      const unitLabel = screen.getByText('LBS')

      // Then: Both labels should have -bottom-6 positioning
      expect(repsLabel).toHaveClass('-bottom-6')
      expect(unitLabel).toHaveClass('-bottom-6')
    })
  })

  describe('Complete Layout Spacing', () => {
    it('should maintain proper vertical rhythm with all spacing changes', () => {
      // Given: CurrentSetCard is rendered
      render(<CurrentSetCard {...defaultProps} />)

      // When: Examining the complete layout
      const explainerText = screen.getByText(
        'Swipe left to skip · right to complete'
      )
      const saveButton = screen.getByText('Save set')
      const repsLabel = screen.getByText('REPS')
      const unitLabel = screen.getByText('KG')

      // Then: All spacing changes should be applied
      // Explainer text
      expect(explainerText).toHaveClass('text-base')
      expect(explainerText.parentElement).toHaveClass('mt-2')

      // Save button
      const buttonWrapper = saveButton.closest('div.w-full')
      expect(buttonWrapper).toHaveClass('mt-2')

      // Label alignment
      expect(repsLabel).toHaveClass('-bottom-6')
      expect(unitLabel).toHaveClass('-bottom-6')
    })
  })
})
