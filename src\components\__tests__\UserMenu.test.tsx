import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { UserMenu } from '../UserMenu'
import { useAuth } from '@/hooks/useAuth'
import { usePWAInstall } from '@/hooks/usePWAInstall'
import { useRouter } from 'next/navigation'

// Mock dependencies
vi.mock('@/hooks/useAuth')
vi.mock('@/hooks/usePWAInstall')
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(),
    has: vi.fn(),
    getAll: vi.fn(),
  })),
}))
vi.mock('@/utils/haptic', () => ({
  triggerHaptic: vi.fn(),
}))
vi.mock('@/utils/clearAllCaches', () => ({
  clearAllCaches: vi.fn().mockResolvedValue(undefined),
  clearAuthCaches: vi.fn().mockResolvedValue(undefined),
}))

describe('UserMenu', () => {
  const mockUser = { email: '<EMAIL>', name: 'Test User' }
  const mockLogout = vi.fn()
  const mockPush = vi.fn()
  const mockOnClose = vi.fn()
  let queryClient: QueryClient

  const renderWithProviders = (ui: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>
    )
  }

  beforeEach(() => {
    vi.clearAllMocks()
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })
    vi.mocked(useAuth).mockReturnValue({
      logout: mockLogout,
      logoutMutation: { isPending: false },
    } as any)
    vi.mocked(usePWAInstall).mockReturnValue({
      canInstall: false,
      installPrompt: vi.fn().mockResolvedValue(true),
      isInstalling: false,
      isInstalled: false,
    } as any)
    vi.mocked(useRouter).mockReturnValue({
      push: mockPush,
    } as any)
  })

  it('should not render when closed', () => {
    renderWithProviders(
      <UserMenu isOpen={false} onClose={mockOnClose} user={mockUser} />
    )

    expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
  })

  it('should render when open', () => {
    renderWithProviders(
      <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
    )

    expect(screen.getByRole('dialog')).toBeInTheDocument()
    expect(screen.getByText('Test User')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })

  it('should display user email when name is not provided', () => {
    const userWithoutName = { email: '<EMAIL>' }

    renderWithProviders(
      <UserMenu isOpen onClose={mockOnClose} user={userWithoutName} />
    )

    // Should show email as the main display name
    const emailElements = screen.getAllByText('<EMAIL>')
    expect(emailElements).toHaveLength(2) // Once as name, once as subtitle
  })

  it('should display user name', () => {
    renderWithProviders(
      <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
    )

    expect(screen.getByText('Test User')).toBeInTheDocument()
  })

  it('should close menu when clicking backdrop', () => {
    renderWithProviders(
      <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
    )

    const backdrop = screen.getByRole('dialog').previousSibling
    fireEvent.click(backdrop!)

    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it('should close menu on escape key', () => {
    renderWithProviders(
      <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
    )

    fireEvent.keyDown(document, { key: 'Escape' })

    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it('should close menu on outside click', () => {
    renderWithProviders(
      <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
    )

    // Click outside the menu
    fireEvent.mouseDown(document.body)

    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it('should not close menu when clicking inside', () => {
    renderWithProviders(
      <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
    )

    const menu = screen.getByRole('dialog')
    fireEvent.mouseDown(menu)

    expect(mockOnClose).not.toHaveBeenCalled()
  })

  it('should handle logout when clicking logout button', async () => {
    const { clearAuthCaches } = await import('@/utils/clearAllCaches')
    vi.mocked(clearAuthCaches).mockResolvedValue(undefined)

    renderWithProviders(
      <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
    )

    const logoutButton = screen.getByText('Log out')
    await fireEvent.click(logoutButton)

    expect(clearAuthCaches).toHaveBeenCalled()
    expect(mockLogout).toHaveBeenCalledTimes(1)
    expect(mockOnClose).toHaveBeenCalledTimes(1)
    expect(mockPush).toHaveBeenCalledWith('/login')
  })

  it('should show loading state when logout is pending', () => {
    vi.mocked(useAuth).mockReturnValue({
      logout: mockLogout,
      logoutMutation: { isPending: true },
    } as any)

    renderWithProviders(
      <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
    )

    const logoutButton = screen.getByText('Logging out...')
    expect(logoutButton).toBeDisabled()
  })

  it('should have proper accessibility attributes', () => {
    renderWithProviders(
      <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
    )

    const menu = screen.getByRole('dialog')
    expect(menu).toHaveAttribute('aria-label', 'User menu')
  })

  it('should have logout icon', () => {
    renderWithProviders(
      <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
    )

    const logoutButton = screen.getByText('Log out').closest('button')
    const svg = logoutButton?.querySelector('svg')
    expect(svg).toBeTruthy()
  })

  describe('PWA Install functionality', () => {
    it('should show install button when app is installable', () => {
      vi.mocked(usePWAInstall).mockReturnValue({
        canInstall: true,
        installPrompt: vi.fn().mockResolvedValue(true),
        isInstalling: false,
        isInstalled: false,
      } as any)

      renderWithProviders(
        <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
      )

      expect(screen.getByText('Install App')).toBeInTheDocument()
    })

    it('should not show install button when app is not installable', () => {
      vi.mocked(usePWAInstall).mockReturnValue({
        canInstall: false,
        installPrompt: vi.fn().mockResolvedValue(true),
        isInstalling: false,
        isInstalled: false,
      } as any)

      renderWithProviders(
        <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
      )

      expect(screen.queryByText('Install App')).not.toBeInTheDocument()
    })

    it('should show installing state when installation is in progress', () => {
      vi.mocked(usePWAInstall).mockReturnValue({
        canInstall: true,
        installPrompt: vi.fn().mockResolvedValue(true),
        isInstalling: true,
        isInstalled: false,
      } as any)

      renderWithProviders(
        <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
      )

      expect(screen.getByText('Installing...')).toBeInTheDocument()
      expect(screen.getByText('Installing...')).toBeDisabled()
    })

    it('should show installed status when app is already installed', () => {
      vi.mocked(usePWAInstall).mockReturnValue({
        canInstall: false,
        installPrompt: vi.fn().mockResolvedValue(true),
        isInstalling: false,
        isInstalled: true,
      } as any)

      renderWithProviders(
        <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
      )

      expect(screen.getByText('App Installed')).toBeInTheDocument()
      expect(screen.queryByText('Install App')).not.toBeInTheDocument()
    })

    it('should trigger install prompt when install button is clicked', async () => {
      const mockInstallPrompt = vi.fn().mockResolvedValue(true)
      vi.mocked(usePWAInstall).mockReturnValue({
        canInstall: true,
        installPrompt: mockInstallPrompt,
        isInstalling: false,
        isInstalled: false,
      } as any)

      renderWithProviders(
        <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
      )

      const installButton = screen.getByText('Install App')
      await fireEvent.click(installButton)

      expect(mockInstallPrompt).toHaveBeenCalledTimes(1)
    })

    it('should close menu after successful installation', async () => {
      const mockInstallPrompt = vi.fn().mockResolvedValue(true)
      vi.mocked(usePWAInstall).mockReturnValue({
        canInstall: true,
        installPrompt: mockInstallPrompt,
        isInstalling: false,
        isInstalled: false,
      } as any)

      renderWithProviders(
        <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
      )

      const installButton = screen.getByText('Install App')
      await fireEvent.click(installButton)

      expect(mockOnClose).toHaveBeenCalledTimes(1)
    })

    it('should not close menu after failed installation', async () => {
      const mockInstallPrompt = vi.fn().mockResolvedValue(false)
      vi.mocked(usePWAInstall).mockReturnValue({
        canInstall: true,
        installPrompt: mockInstallPrompt,
        isInstalling: false,
        isInstalled: false,
      } as any)

      renderWithProviders(
        <UserMenu isOpen onClose={mockOnClose} user={mockUser} />
      )

      const installButton = screen.getByText('Install App')
      await fireEvent.click(installButton)

      expect(mockInstallPrompt).toHaveBeenCalledTimes(1)
      expect(mockOnClose).not.toHaveBeenCalled()
    })
  })
})
