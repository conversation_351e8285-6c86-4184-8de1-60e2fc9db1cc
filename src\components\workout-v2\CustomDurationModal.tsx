'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X } from 'lucide-react'
import { vibrate } from '@/utils/haptics'

interface CustomDurationModalProps {
  currentDuration: number
  onSelect: (duration: number) => void
  onClose: () => void
}

export function CustomDurationModal({
  currentDuration,
  onSelect,
  onClose,
}: CustomDurationModalProps) {
  const [value, setValue] = useState(currentDuration.toString())
  const [error, setError] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    // Focus input on mount
    inputRef.current?.focus()
    inputRef.current?.select()
  }, [])

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onClose])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    const numValue = parseFloat(value)

    if (Number.isNaN(numValue)) {
      setError('Please enter a valid number')
      vibrate('error')
      return
    }

    const seconds = Math.round(numValue)

    if (seconds < 5 || seconds > 600) {
      setError('Please enter a value between 5 and 600 seconds')
      vibrate('error')
      return
    }

    vibrate('medium')
    onSelect(seconds)
    onClose()
  }

  return (
    <AnimatePresence>
      {/* Backdrop */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 z-50"
        onClick={onClose}
      />

      {/* Modal */}
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: 'spring', damping: 25, stiffness: 300 }}
        data-testid="custom-duration-modal"
        className="fixed inset-x-4 top-[10vh] 
                   w-auto max-w-sm max-h-[50vh] mx-auto
                   bg-surface-primary rounded-3xl shadow-2xl z-60 p-6
                   overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-text-primary">
            Custom Duration
          </h3>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-surface-secondary transition-colors"
          >
            <X className="w-5 h-5 text-text-secondary" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <label
              htmlFor="duration-input"
              className="block text-sm font-medium text-text-secondary mb-2"
            >
              Enter duration in seconds
            </label>
            <input
              ref={inputRef}
              id="duration-input"
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              value={value}
              onChange={(e) => {
                setValue(e.target.value)
                setError('')
              }}
              className="w-full px-4 py-3 bg-surface-secondary rounded-2xl
                       text-black dark:text-text-primary text-lg font-medium text-center
                       focus:outline-none focus:ring-2 focus:ring-brand-gold-start
                       transition-all"
              placeholder="90"
            />
            {error && (
              <p className="mt-2 text-sm text-error text-center">{error}</p>
            )}
            <p className="mt-2 text-xs text-text-tertiary text-center">
              Min: 5 seconds • Max: 600 seconds (10 minutes)
            </p>
          </div>

          {/* Actions */}
          <div className="flex gap-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 py-3 px-6 rounded-2xl font-medium text-base
                       bg-surface-secondary text-text-primary
                       hover:bg-surface-tertiary transition-all
                       min-h-[48px] touch-manipulation"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 py-3 px-6 rounded-2xl font-medium text-base
                       bg-gradient-to-r from-brand-gold-start to-brand-gold-end
                       text-text-inverse shadow-lg hover:shadow-xl
                       transition-all min-h-[48px] touch-manipulation"
            >
              Confirm
            </button>
          </div>
        </form>
      </motion.div>
    </AnimatePresence>
  )
}
