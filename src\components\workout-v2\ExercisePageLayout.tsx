'use client'

import type {
  ExerciseModel,
  WorkoutLogSerieModel,
  RecommendationModel,
} from '@/types'
import { CurrentSetCard } from './CurrentSetCard'
import { NextSetsPreview } from './NextSetsPreview'
import { RestTimer } from './RestTimer'

interface ExercisePageLayoutProps {
  currentExercise: ExerciseModel
  currentSet: WorkoutLogSerieModel | null
  allSets: WorkoutLogSerieModel[]
  setData: {
    reps: number
    weight: number
    duration: number
  }
  onSetDataChange: (data: {
    reps: number
    weight: number
    duration: number
  }) => void
  handleCompleteSet: () => void
  handleSkipSet: () => void
  isSaving: boolean
  completedCount: number
  unit: 'kg' | 'lbs'
  recommendation: RecommendationModel | null
  currentSetIndex: number
  isWarmup: boolean
  isFirstWorkSet: boolean
  saveError: string | null
}

export function ExercisePageLayout({
  currentExercise,
  currentSet,
  allSets,
  setData,
  onSetDataChange,
  handleCompleteSet,
  handleSkipSet,
  isSaving,
  completedCount,
  unit,
  recommendation,
  currentSetIndex,
  isWarmup,
  isFirstWorkSet,
  saveError,
}: ExercisePageLayoutProps) {
  return (
    <div
      className="flex flex-col h-screen bg-surface-primary"
      data-testid="exercise-page-container"
    >
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col px-4 pt-4 overflow-hidden">
        {/* Hybrid View - Current set + Next sets preview */}
        <div className="flex-1 flex flex-col">
          <div className="flex-[6] flex items-center justify-center">
            <CurrentSetCard
              exercise={currentExercise}
              currentSet={currentSet}
              setData={setData}
              onSetDataChange={onSetDataChange}
              onComplete={handleCompleteSet}
              onSkip={handleSkipSet}
              isSaving={isSaving}
              completedSets={completedCount}
              unit={unit}
              recommendation={recommendation}
              currentSetIndex={currentSetIndex}
              isWarmup={isWarmup}
              isFirstWorkSet={isFirstWorkSet}
            />
          </div>
          <div className="flex-[3] flex items-start justify-center pb-4">
            <NextSetsPreview
              nextSets={allSets.filter((s) => !s.IsFinished && !s.IsNext)}
              unit={unit}
              currentSetIndex={completedCount}
              recommendation={recommendation}
            />
          </div>
        </div>

        {/* Error message */}
        {saveError && <p className="text-error text-sm mt-2">{saveError}</p>}

        {/* Rest Timer at bottom (shows when active) */}
        <RestTimer />
      </div>
    </div>
  )
}
